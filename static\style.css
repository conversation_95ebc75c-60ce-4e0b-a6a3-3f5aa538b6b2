/* Task Assignment System - Gemini-inspired Dark Theme */
:root {
    /* Color scheme from sample.css */
    --primary-bg: #131314;
    --secondary-bg: #1e1f20;
    --surface-bg: #2f3030;
    --surface-hover: #3c4043;
    --border-color: #3c4043;
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --text-muted: #5f6368;
    --accent-blue: #8ab4f8;
    --accent-green: #81c995;
    --accent-red: #f28b82;
    --accent-yellow: #fdd663;
    --input-bg: #303134;
    --input-border: #5f6368;
    --input-focus: #8ab4f8;
    --shadow: rgba(0, 0, 0, 0.3);
    
    /* Font sizes */
    --font-size-small: 14px;
    --font-size-medium: 16px;
    --font-size-large: 18px;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Google Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* Navigation Bar */
.navbar {
    background-color: var(--secondary-bg) !important;
    border-bottom: 1px solid var(--border-color);
    padding: 16px 0;
}

.navbar-brand {
    color: var(--text-primary) !important;
    font-size: 22px;
    font-weight: 500;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--text-secondary) !important;
    font-weight: 400;
    padding: 8px 16px !important;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--text-primary) !important;
    background-color: var(--surface-hover);
}

.navbar-nav .nav-link.active {
    color: var(--accent-blue) !important;
    background-color: rgba(138, 180, 248, 0.1);
}

/* Main Container */
.container-fluid {
    background-color: var(--primary-bg);
    min-height: calc(100vh - 80px);
    padding: 24px;
}

/* Page Headers */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 16px;
}

h1 {
    font-size: 32px;
    background: linear-gradient(45deg, var(--accent-blue), var(--accent-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Cards and Panels */
.card {
    background-color: var(--surface-bg) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px var(--shadow);
}

.card-header {
    background-color: var(--secondary-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    color: var(--text-primary);
    font-weight: 500;
    padding: 16px 20px;
}

.card-body {
    padding: 20px;
}

/* Tables */
.table {
    background-color: var(--surface-bg);
    color: var(--text-primary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px var(--shadow);
}

.table thead th {
    background-color: var(--secondary-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
    padding: 16px;
    font-size: var(--font-size-medium);
}

.table tbody td {
    background-color: var(--surface-bg) !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--border-color);
    padding: 16px;
    vertical-align: middle;
    font-weight: 400;
}

.table-striped tbody tr:nth-of-type(odd) td {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: var(--text-primary) !important;
}

.table-hover tbody tr:hover td {
    background-color: var(--surface-hover) !important;
    color: var(--text-primary) !important;
}

/* Ensure all table text is readable */
.table td, .table th {
    color: var(--text-primary) !important;
}

.table .badge {
    font-weight: 500;
    color: var(--primary-bg) !important;
}

/* Action buttons styling */
.action-buttons {
    white-space: nowrap;
    min-width: 180px;
}

.action-buttons .btn {
    margin-right: 8px;
    margin-bottom: 4px;
}

/* Buttons */
.btn {
    border-radius: 8px !important;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background-color: var(--accent-blue) !important;
    border-color: var(--accent-blue) !important;
    color: var(--primary-bg) !important;
}

.btn-primary:hover {
    background-color: #6fa8f5 !important;
    border-color: #6fa8f5 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(138, 180, 248, 0.3);
}

.btn-success {
    background-color: var(--accent-green) !important;
    border-color: var(--accent-green) !important;
    color: var(--primary-bg) !important;
}

.btn-success:hover {
    background-color: #6bb885 !important;
    border-color: #6bb885 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(129, 201, 149, 0.3);
}

.btn-danger {
    background-color: var(--accent-red) !important;
    border-color: var(--accent-red) !important;
    color: var(--primary-bg) !important;
}

.btn-danger:hover {
    background-color: #ef7a70 !important;
    border-color: #ef7a70 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(242, 139, 130, 0.3);
}

.btn-warning {
    background-color: var(--accent-yellow) !important;
    border-color: var(--accent-yellow) !important;
    color: var(--primary-bg) !important;
}

.btn-warning:hover {
    background-color: #fcd34d !important;
    border-color: #fcd34d !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(253, 214, 99, 0.3);
}

.btn-outline-primary {
    background-color: transparent !important;
    border-color: var(--accent-blue) !important;
    color: var(--accent-blue) !important;
}

.btn-outline-primary:hover {
    background-color: var(--accent-blue) !important;
    color: var(--primary-bg) !important;
}

.btn-outline-warning {
    background-color: transparent !important;
    border-color: var(--accent-yellow) !important;
    color: var(--accent-yellow) !important;
}

.btn-outline-warning:hover {
    background-color: var(--accent-yellow) !important;
    color: var(--primary-bg) !important;
}

/* Forms */
.form-control {
    background-color: var(--input-bg) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: 8px !important;
    color: var(--text-primary) !important;
    padding: 12px 16px;
    transition: all 0.2s ease;
}

.form-control:focus {
    background-color: var(--input-bg) !important;
    border-color: var(--input-focus) !important;
    box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.2) !important;
    color: var(--text-primary) !important;
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 8px;
}

.form-select {
    background-color: var(--input-bg) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: 8px !important;
    color: var(--text-primary) !important;
    padding: 12px 16px;
}

.form-select:focus {
    border-color: var(--input-focus) !important;
    box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.2) !important;
}

/* Badges */
.badge {
    border-radius: 6px !important;
    font-weight: 500;
    padding: 6px 10px;
}

.badge.bg-success {
    background-color: var(--accent-green) !important;
    color: var(--primary-bg) !important;
}

.badge.bg-warning {
    background-color: var(--accent-yellow) !important;
    color: var(--primary-bg) !important;
}

.badge.bg-danger {
    background-color: var(--accent-red) !important;
    color: var(--primary-bg) !important;
}

.badge.bg-secondary {
    background-color: var(--text-muted) !important;
    color: var(--text-primary) !important;
}

/* Status indicators */
#waha-status .badge {
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 8px;
}

/* Mobile-specific status badge */
@media (max-width: 768px) {
    #waha-status .badge {
        font-size: 12px;
        padding: 6px 8px;
        white-space: nowrap;
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Compact status text for mobile */
    #waha-status .badge .fab {
        margin-right: 4px;
    }
}

/* Alerts */
.alert {
    border-radius: 8px !important;
    border: 1px solid;
    padding: 16px 20px;
}

.alert-success {
    background-color: rgba(129, 201, 149, 0.1) !important;
    border-color: rgba(129, 201, 149, 0.3) !important;
    color: var(--accent-green) !important;
}

.alert-danger {
    background-color: rgba(242, 139, 130, 0.1) !important;
    border-color: rgba(242, 139, 130, 0.3) !important;
    color: var(--accent-red) !important;
}

.alert-warning {
    background-color: rgba(253, 214, 99, 0.1) !important;
    border-color: rgba(253, 214, 99, 0.3) !important;
    color: var(--accent-yellow) !important;
}

.alert-info {
    background-color: rgba(138, 180, 248, 0.1) !important;
    border-color: rgba(138, 180, 248, 0.3) !important;
    color: var(--accent-blue) !important;
}

/* Empty state */
.text-center.py-5 {
    padding: 48px 24px !important;
    background-color: var(--surface-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.text-muted {
    color: var(--text-secondary) !important;
}

/* Icons */
.fas, .fab {
    margin-right: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 16px;
    }

    h1 {
        font-size: 24px;
    }

    /* Mobile header layout */
    .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 12px;
    }

    .d-flex.justify-content-between.align-items-center > div:first-child {
        width: 100%;
    }

    .d-flex.justify-content-between.align-items-center > a {
        width: 100%;
        text-align: center;
    }

    /* Mobile status layout */
    #waha-status {
        margin-top: 8px !important;
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    #restart-waha-btn {
        font-size: 12px !important;
        padding: 4px 8px !important;
        margin-left: 0 !important;
    }

    /* Mobile button sizing */
    .btn {
        font-size: 14px;
        padding: 10px 16px;
    }

    .btn-sm {
        font-size: 12px;
        padding: 6px 12px;
    }

    .table thead th,
    .table tbody td {
        padding: 12px 8px;
        font-size: 14px;
    }

    .action-buttons .btn {
        margin-right: 4px;
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .table {
    animation: fadeIn 0.3s ease-in;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--primary-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--surface-hover);
}

#!/usr/bin/env python3
"""
MCP Server for Task Assignment System

This server provides MCP (Model Context Protocol) tools for managing tasks and employees
in the task assignment system. It acts as a bridge between LLMs and the Flask application.

Supported Tools:
- Level 1 Core: create_task, add_employee
- Level 2 Core: edit_task, complete_task, delete_task, edit_employee, delete_employee
- Additional: get_task_list, get_employee_list
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
import sys
import os
from flask import Flask, jsonify

# Add the current directory to Python path to import from app.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from mcp.server import Server
    from mcp.server.models import InitializationOptions
    from mcp.server.stdio import stdio_server
    from mcp.types import (
        Resource,
        Tool,
        TextContent,
        ImageContent,
        EmbeddedResource,
        LoggingLevel
    )
except ImportError:
    print("MCP library not found. Please install with: pip install mcp")
    sys.exit(1)

# Import Flask app components
try:
    from app import app, db, Task, Employee, TaskAssignment
    from app import (
        normalize_phone_number,
        validate_malaysian_phone,
        schedule_task_notifications
    )
except ImportError as e:
    print(f"Error importing from app.py: {e}")
    print("Make sure app.py is in the same directory and all dependencies are installed")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("mcp-task-server")

# Initialize MCP Server
server = Server("task-assignment-server")

class TaskAssignmentMCPServer:
    """
    MCP Server for Task Assignment System

    Provides tools for managing tasks and employees through MCP protocol.
    """

    def __init__(self):
        self.app = app
        self.db = db
        logger.info("Task Assignment MCP Server initialized")

    def get_app_context(self):
        """Get Flask application context for database operations"""
        return self.app.app_context()

# Tool Definitions
@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """
    List all available MCP tools for the task assignment system.

    Returns:
        List[Tool]: Available tools categorized by functionality level
    """
    return [
        # Level 1 Core Functions
        Tool(
            name="create_task",
            description="Create a new task with description, deadline, notification settings, and assign to employees",
            inputSchema={
                "type": "object",
                "properties": {
                    "description": {
                        "type": "string",
                        "description": "Task description"
                    },
                    "deadline": {
                        "type": "string",
                        "description": "Task deadline in YYYY-MM-DD HH:MM format"
                    },
                    "notification_type": {
                        "type": "string",
                        "enum": ["daily", "weekly", "days_before", "every_n_days"],
                        "description": "Type of notification schedule"
                    },
                    "notification_value": {
                        "type": "integer",
                        "description": "Number of days for 'days_before' or 'every_n_days' notification types",
                        "minimum": 1
                    },
                    "employee_ids": {
                        "type": "array",
                        "items": {"type": "integer"},
                        "description": "List of employee IDs to assign the task to"
                    }
                },
                "required": ["description", "deadline", "notification_type", "employee_ids"]
            }
        ),

        Tool(
            name="add_employee",
            description="Add a new employee with name, email, and phone number",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Employee full name"
                    },
                    "email": {
                        "type": "string",
                        "format": "email",
                        "description": "Employee email address (must be unique)"
                    },
                    "phone": {
                        "type": "string",
                        "description": "Malaysian phone number (e.g., 0123456789 or 123456789)"
                    }
                },
                "required": ["name", "email", "phone"]
            }
        ),

        # Level 2 Core Functions
        Tool(
            name="edit_task",
            description="Edit an existing task's details, deadline, notification settings, or assigned employees",
            inputSchema={
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "ID of the task to edit"
                    },
                    "description": {
                        "type": "string",
                        "description": "Updated task description"
                    },
                    "deadline": {
                        "type": "string",
                        "description": "Updated deadline in YYYY-MM-DD HH:MM format"
                    },
                    "notification_type": {
                        "type": "string",
                        "enum": ["daily", "weekly", "days_before", "every_n_days"],
                        "description": "Updated notification schedule type"
                    },
                    "notification_value": {
                        "type": "integer",
                        "description": "Updated number of days for notification",
                        "minimum": 1
                    },
                    "employee_ids": {
                        "type": "array",
                        "items": {"type": "integer"},
                        "description": "Updated list of employee IDs assigned to the task"
                    }
                },
                "required": ["task_id"]
            }
        ),

        Tool(
            name="complete_task",
            description="Mark a task as completed",
            inputSchema={
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "ID of the task to mark as completed"
                    }
                },
                "required": ["task_id"]
            }
        ),

        Tool(
            name="delete_task",
            description="Delete a task permanently",
            inputSchema={
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "ID of the task to delete"
                    }
                },
                "required": ["task_id"]
            }
        ),

        Tool(
            name="edit_employee",
            description="Edit an existing employee's information",
            inputSchema={
                "type": "object",
                "properties": {
                    "employee_id": {
                        "type": "integer",
                        "description": "ID of the employee to edit"
                    },
                    "name": {
                        "type": "string",
                        "description": "Updated employee name"
                    },
                    "email": {
                        "type": "string",
                        "format": "email",
                        "description": "Updated employee email"
                    },
                    "phone": {
                        "type": "string",
                        "description": "Updated Malaysian phone number"
                    }
                },
                "required": ["employee_id"]
            }
        ),

        Tool(
            name="delete_employee",
            description="Delete an employee permanently (also removes from all assigned tasks)",
            inputSchema={
                "type": "object",
                "properties": {
                    "employee_id": {
                        "type": "integer",
                        "description": "ID of the employee to delete"
                    }
                },
                "required": ["employee_id"]
            }
        ),

        # Additional Functions
        Tool(
            name="get_task_list",
            description="Get a list of all tasks with their details and assigned employees",
            inputSchema={
                "type": "object",
                "properties": {
                    "include_completed": {
                        "type": "boolean",
                        "description": "Whether to include completed tasks in the list",
                        "default": True
                    },
                    "employee_id": {
                        "type": "integer",
                        "description": "Filter tasks by specific employee ID (optional)"
                    }
                }
            }
        ),

        Tool(
            name="get_employee_list",
            description="Get a list of all employees with their contact information",
            inputSchema={
                "type": "object",
                "properties": {
                    "include_task_count": {
                        "type": "boolean",
                        "description": "Whether to include the number of assigned tasks for each employee",
                        "default": False
                    }
                }
            }
        )
    ]

# HTTP Endpoint for n8n AI Agent
def create_tools_endpoint():
    """
    Create HTTP endpoint to expose available tools for n8n AI agent

    Returns:
        Flask app with /tools endpoint
    """
    from flask import Flask

    # Create a separate Flask app for the HTTP endpoint
    http_app = Flask("mcp-tools-api")

    @http_app.route('/tools', methods=['GET'])
    def get_tools():
        """
        HTTP endpoint to get available MCP tools

        Returns:
            JSON response with tools list and metadata
        """
        try:
            # Get the tools list synchronously by running the async function
            import asyncio
            tools_list = asyncio.run(handle_list_tools())

            # Convert tools to JSON-serializable format
            tools_data = []
            for tool in tools_list:
                tool_data = {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                }
                tools_data.append(tool_data)

            response = {
                "status": "success",
                "server_name": "task-assignment-server",
                "server_version": "1.0.0",
                "tools_count": len(tools_data),
                "tools": tools_data,
                "categories": {
                    "level_1_core": ["create_task", "add_employee"],
                    "level_2_core": ["edit_task", "complete_task", "delete_task", "edit_employee", "delete_employee"],
                    "additional": ["get_task_list", "get_employee_list"]
                }
            }

            return jsonify(response)

        except Exception as e:
            logger.error(f"Error in /tools endpoint: {str(e)}")
            return jsonify({
                "status": "error",
                "error": str(e),
                "server_name": "task-assignment-server"
            }), 500

    return http_app

# Tool Call Handlers (Structure only - implementation will be added later)
@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """
    Handle MCP tool calls.

    Args:
        name: Name of the tool to call
        arguments: Tool arguments

    Returns:
        List[TextContent]: Tool execution results
    """

    mcp_server = TaskAssignmentMCPServer()

    try:
        with mcp_server.get_app_context():
            if name == "create_task":
                return await handle_create_task(arguments)
            elif name == "add_employee":
                return await handle_add_employee(arguments)
            elif name == "edit_task":
                return await handle_edit_task(arguments)
            elif name == "complete_task":
                return await handle_complete_task(arguments)
            elif name == "delete_task":
                return await handle_delete_task(arguments)
            elif name == "edit_employee":
                return await handle_edit_employee(arguments)
            elif name == "delete_employee":
                return await handle_delete_employee(arguments)
            elif name == "get_task_list":
                return await handle_get_task_list(arguments)
            elif name == "get_employee_list":
                return await handle_get_employee_list(arguments)
            else:
                raise ValueError(f"Unknown tool: {name}")

    except Exception as e:
        logger.error(f"Error executing tool {name}: {str(e)}")
        return [TextContent(
            type="text",
            text=f"Error executing {name}: {str(e)}"
        )]

# Tool Implementation Placeholders (to be implemented later)
async def handle_create_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle create_task tool call - Implementation pending"""
    return [TextContent(type="text", text="create_task implementation pending")]

async def handle_add_employee(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle add_employee tool call - Implementation pending"""
    return [TextContent(type="text", text="add_employee implementation pending")]

async def handle_edit_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle edit_task tool call - Implementation pending"""
    return [TextContent(type="text", text="edit_task implementation pending")]

async def handle_complete_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle complete_task tool call - Implementation pending"""
    return [TextContent(type="text", text="complete_task implementation pending")]

async def handle_delete_task(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle delete_task tool call - Implementation pending"""
    return [TextContent(type="text", text="delete_task implementation pending")]

async def handle_edit_employee(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle edit_employee tool call - Implementation pending"""
    return [TextContent(type="text", text="edit_employee implementation pending")]

async def handle_delete_employee(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle delete_employee tool call - Implementation pending"""
    return [TextContent(type="text", text="delete_employee implementation pending")]

async def handle_get_task_list(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle get_task_list tool call - Implementation pending"""
    return [TextContent(type="text", text="get_task_list implementation pending")]

async def handle_get_employee_list(arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle get_employee_list tool call - Implementation pending"""
    return [TextContent(type="text", text="get_employee_list implementation pending")]

# HTTP Server for n8n AI Agent
def run_http_server(host="localhost", port=5005):
    """
    Run HTTP server to expose tools endpoint for n8n AI agent

    Args:
        host: Server host (default: localhost)
        port: Server port (default: 5005)
    """
    logger.info(f"Starting HTTP Tools API Server on {host}:{port}")
    logger.info(f"Tools endpoint available at: http://{host}:{port}/tools")

    http_app = create_tools_endpoint()
    http_app.run(host=host, port=port, debug=False)

# Server Entry Point
async def main():
    """Main entry point for the MCP server"""
    logger.info("Starting Task Assignment MCP Server...")

    # Initialize server options
    options = InitializationOptions(
        server_name="task-assignment-server",
        server_version="1.0.0",
        capabilities={
            "tools": {}
        }
    )

    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            options
        )

if __name__ == "__main__":
    import sys

    # Check command line arguments to determine mode
    if len(sys.argv) > 1 and sys.argv[1] == "--http":
        # Run HTTP server mode for n8n AI agent
        host = sys.argv[2] if len(sys.argv) > 2 else "localhost"
        port = int(sys.argv[3]) if len(sys.argv) > 3 else 5105
        run_http_server(host, port)
    else:
        # Run MCP server mode (default)
        asyncio.run(main())